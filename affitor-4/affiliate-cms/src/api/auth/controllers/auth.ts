export default {
  // Get the URL that redirects to Google OAuth page
  getGoogleAuthUrl: async (ctx) => {
    const authUrl = strapi.service('api::auth.google-auth').getAuthUrl();
    ctx.body = { googleAuthUrl: authUrl };
  },

  // Handle the Google OAuth callback
  googleCallback: async (ctx) => {
    const { code, error } = ctx.query;

    if (error === 'access_denied') {
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(
        `${frontendUrl}/auth/google/callback?message=${encodeURIComponent('Access denied by user')}`
      );
    }

    if (!code) {
      return ctx.badRequest('Authorization code not provided');
    }

    try {
      // Authenticate with Google
      const googleAuthService = strapi.service('api::auth.google-auth');
      const { jwt, user } = await googleAuthService.authenticate(code);

      // IMPORTANT: After successful authentication, redirect to frontend with the token
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(`${frontendUrl}/authentication/google?token=${jwt}&userId=${user.id}`);
    } catch (error) {
      console.error('Google authentication error:', error);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';
      return ctx.redirect(
        `${frontendUrl}/authentication/google?message=${encodeURIComponent(error.message)}`
      );
    }
  },

  // Redirect to frontend after authentication
  googleRedirect: async (ctx) => {
    const { jwt, user } = ctx.state;
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

    // Redirect to frontend with token as query parameter
    ctx.redirect(`${frontendUrl}/authentication/google?token=${jwt}&userId=${user.id}`);
  },

  /**
   * Validates JWT token and returns user information
   */
  validateToken: async (ctx) => {
    try {
      const { authorization } = ctx.request.header;

      if (!authorization) {
        return ctx.badRequest('Authorization header is missing');
      }

      // Extract the token from the Authorization header
      const token = authorization.split(' ')[1];

      console.log('LOG-token', token);

      if (!token) {
        return ctx.badRequest('Token is missing');
      }

      // Verify and decode the token
      const { id, exp } = await strapi.plugins['users-permissions'].services.jwt.verify(token);

      // Get the user from the database
      const user = await strapi.query('plugin::users-permissions.user').findOne({ where: { id } });

      if (!user) {
        return ctx.notFound('User not found');
      }

      // Manually sanitize the user object by excluding sensitive information
      const { password, resetPasswordToken, confirmationToken, ...sanitizedUser } = user;

      return {
        user: sanitizedUser,
        expiresAt: new Date(exp * 1000).toISOString(),
      };
    } catch (error) {
      console.error('Error validating token:', error);
      if (error.message === 'jwt expired') {
        return ctx.unauthorized('Token expired');
      }
      return ctx.badRequest('Invalid token');
    }
  },

  /**
   * Sign up a new user with email and password
   */
  signup: async (ctx) => {
    const { email, password, fullname } = ctx.request.body;

    try {
      // Check if user already exists
      const existingUser = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: email.toLowerCase() },
      });

      if (existingUser) {
        return ctx.badRequest('Email is already taken');
      }

      // Generate verification token
      const verificationToken =
        Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

      // Prepare base user data
      const baseUserData = {
        username: email.toLowerCase(), // Use email as username
        email: email.toLowerCase(),
        password,
        fullname,
        confirmed: true,
        blocked: false,
        role: await strapi
          .query('plugin::users-permissions.role')
          .findOne({ where: { type: 'authenticated' } }),
        verificationToken,
      };

      // Get user data with defaults (including subscription tier)
      const userDefaultsService = strapi.service('api::auth.user-defaults');
      const userData = await userDefaultsService.getUserDataWithDefaults(baseUserData);

      // Create user
      const user = await strapi.plugins['users-permissions'].services.user.add(userData);

      // Manually sanitize the user object by excluding sensitive information
      const { ...sanitizedUser } = user;

      // Send verification email using direct Firebase service (not as a plugin)
      try {
        const firebaseEmailService = strapi.service('api::auth.firebase-email');
        await firebaseEmailService.sendVerificationEmail(user, verificationToken);
      } catch (error) {
        console.error('Error sending verification email:', error);
        // Continue with the registration process even if email sending fails
      }

      return {
        user: sanitizedUser,
        message: 'Registration successful. Please verify your email address.',
      };
    } catch (error) {
      console.error('Error during signup:', error);
      return ctx.badRequest('An error occurred during registration');
    }
  },

  /**
   * Sign in with email and password
   */
  signin: async (ctx) => {
    const { email, password, rememberMe } = ctx.request.body;

    try {
      // Find the user
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: email.toLowerCase() },
      });

      if (!user) {
        return ctx.badRequest('Invalid email or password');
      }

      if (!user.confirmed) {
        return ctx.badRequest('Please verify your email before signing in');
      }

      if (user.blocked) {
        return ctx.badRequest('Your account has been blocked');
      }

      // Validate password
      const validPassword = await strapi.plugins[
        'users-permissions'
      ].services.user.validatePassword(password, user.password);

      if (!validPassword) {
        return ctx.badRequest('Invalid email or password');
      }

      // Set token expiration time based on "remember me" option
      const expiresIn = rememberMe ? '30d' : '1d';

      // Generate JWT token
      const token = strapi.plugins['users-permissions'].services.jwt.issue(
        {
          id: user.id,
        },
        { expiresIn }
      );

      // Manually sanitize the user object by excluding sensitive information
      const { ...sanitizedUser } = user;

      return {
        jwt: token,
        user: sanitizedUser,
        expiresIn,
      };
    } catch (error) {
      console.error('Error during signin:', error);
      return ctx.badRequest('An error occurred during sign in');
    }
  },

  /**
   * Verify email address using verification token
   */
  verifyEmail: async (ctx) => {
    const { token } = ctx.query;
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:3000';

    if (!token) {
      return ctx.redirect(
        `${frontendUrl}/auth/verification?status=failed&message=${encodeURIComponent('Verification token is missing')}`
      );
    }

    try {
      // Find user with the verification token
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { verificationToken: token },
      });

      if (!user) {
        return ctx.redirect(
          `${frontendUrl}/auth/verification?status=failed&message=${encodeURIComponent('Invalid verification token')}`
        );
      }

      // Update user to confirmed status
      await strapi.query('plugin::users-permissions.user').update({
        where: { id: user.id },
        data: {
          confirmed: true,
          verificationToken: null,
        },
      });

      // Redirect to frontend with success status
      return ctx.redirect(`${frontendUrl}/auth/verification?status=success`);
    } catch (error) {
      console.error('Error verifying email:', error);
      return ctx.redirect(
        `${frontendUrl}/auth/verification?status=failed&message=${encodeURIComponent('Error verifying email')}`
      );
    }
  },

  /**
   * Get Firebase authentication URL for Google Sign-in
   */
  getFirebaseGoogleAuthUrl: async (ctx) => {
    const firebaseAuthService = strapi.service('api::auth.firebase-auth');
    const authUrl = firebaseAuthService.getGoogleAuthUrl();
    ctx.body = { googleAuthUrl: authUrl };
  },

  /**
   * Authenticate with Firebase token (from any provider)
   */
  firebaseAuthenticate: async (ctx) => {
    const { idToken } = ctx.request.body;

    if (!idToken) {
      return ctx.badRequest('Firebase ID token is required');
    }

    try {
      const firebaseAuthService = strapi.service('api::auth.firebase-auth');
      // Pass the context to the service for cookie access
      const { jwt, user } = await firebaseAuthService.authenticate(idToken, ctx);

      return {
        jwt,
        user,
      };
    } catch (error) {
      console.error('Firebase authentication error:', error);
      return ctx.badRequest(error.message);
    }
  },

  /**
   * Handle redirect after Firebase Google authentication (client-side flow)
   */
  firebaseGoogleCallback: async (ctx) => {
    const { idToken } = ctx.request.body;

    if (!idToken) {
      return ctx.badRequest('Firebase ID token is required');
    }

    try {
      const firebaseAuthService = strapi.service('api::auth.firebase-auth');
      const { jwt, user } = await firebaseAuthService.authenticate(idToken);

      return {
        jwt,
        user,
      };
    } catch (error) {
      console.error('Firebase Google authentication error:', error);
      return ctx.badRequest(error.message);
    }
  },
};
