/**
 * User Defaults Service
 * Handles default values for new user creation
 */
export default {
  /**
   * Get the default subscription tier ID for new users
   * @returns {Promise<number|null>} The subscription tier ID or null if not found
   */
  async getDefaultSubscriptionTier(): Promise<number | null> {
    try {
      const tiers = await strapi.entityService.findMany('api::subscription-tier.subscription-tier', {
        filters: {
          name: 'basic-month',
          publishedAt: { $ne: null },
        },
        limit: 1,
      });
      
      if (tiers && tiers.length > 0) {
        return tiers[0].id;
      }
      
      strapi.log.warn('Default subscription tier "basic-month" not found');
      return null;
    } catch (error) {
      strapi.log.error('Error fetching default subscription tier:', error);
      return null;
    }
  },

  /**
   * Get default user data with subscription tier
   * @param {object} baseUserData - Base user data
   * @returns {Promise<object>} User data with default subscription tier
   */
  async getUserDataWithDefaults(baseUserData: any): Promise<any> {
    const defaultSubscriptionTier = await this.getDefaultSubscriptionTier();
    
    const userData = { ...baseUserData };
    
    if (defaultSubscriptionTier) {
      userData.subscription_tier = defaultSubscriptionTier;
    }
    
    return userData;
  },
};
