# Automatic Subscription Tier Assignment for New Users

## Overview

This implementation automatically assigns the "basic-month" subscription tier to all newly registered users across all registration methods in the system.

## Changes Made

### 1. Created User Defaults Service

**File**: `src/api/auth/services/user-defaults.ts`

A centralized service that handles default values for new user creation:

- `getDefaultSubscriptionTier()`: Retrieves the "basic-month" subscription tier ID
- `getUserDataWithDefaults(baseUserData)`: Adds default subscription tier to user data

### 2. Updated User Creation Points

The following services have been updated to use the user-defaults service:

#### Firebase Authentication
**File**: `src/api/auth/services/firebase-auth.ts`
- Updated to assign default subscription tier when creating users via Firebase auth

#### Email/Password Registration  
**File**: `src/api/auth/controllers/auth.ts`
- Updated signup endpoint to assign default subscription tier

#### Google Authentication
**File**: `src/api/auth/services/google-auth.ts`  
- Updated to assign default subscription tier when creating users via Google auth

#### Airtable User Creation
**File**: `src/api/airtable/services/airtable.ts`
- Updated to assign default subscription tier when creating users from Airtable submissions

## How It Works

1. When a new user registers through any method, the system:
   - Creates base user data with required fields
   - Calls `userDefaultsService.getUserDataWithDefaults(baseUserData)`
   - The service automatically adds the "basic-month" subscription tier ID
   - User is created with the default subscription tier assigned

2. If the "basic-month" subscription tier doesn't exist:
   - The system logs a warning but continues user creation
   - User is created without a subscription tier (graceful degradation)

## Prerequisites

### Required Subscription Tier

A subscription tier with the following properties must exist in the system:

- **Name**: `basic-month`
- **Status**: Published (not draft)
- **Type**: Monthly subscription

### Creating the Basic Month Tier

1. Access the Strapi admin panel
2. Navigate to Content Manager → Subscription Tiers
3. Create a new subscription tier with:
   - Name: `basic-month`
   - Display Name: `Basic Monthly` (or similar)
   - Price: Set appropriate price
   - Request Limit: Set appropriate limit
   - Duration Days: `30`
   - Stripe Recurring Interval: `month`
   - Status: Published

## Testing

### Verification Script

Run the verification script to check implementation:

```bash
node verify-implementation.js
```

### Manual Testing

1. Register a new user via any method:
   - Email/password registration
   - Firebase authentication
   - Google authentication
   - Airtable submission

2. Check the user's subscription tier in the admin panel or via API

3. Verify the user has the "basic-month" subscription tier assigned

## Error Handling

- If "basic-month" tier doesn't exist: User created without subscription tier, warning logged
- If user-defaults service fails: User created without subscription tier, error logged
- All user creation processes continue even if subscription tier assignment fails

## Maintenance

### Adding New User Creation Points

If new user creation methods are added:

1. Import the user-defaults service:
   ```typescript
   const userDefaultsService = strapi.service('api::auth.user-defaults');
   ```

2. Use the service before creating the user:
   ```typescript
   const userData = await userDefaultsService.getUserDataWithDefaults(baseUserData);
   ```

### Changing Default Subscription Tier

To change the default subscription tier:

1. Update the tier name in `src/api/auth/services/user-defaults.ts`
2. Change `'basic-month'` to the new tier name
3. Ensure the new tier exists and is published

## Monitoring

Monitor application logs for:
- Warnings about missing "basic-month" subscription tier
- Errors in user-defaults service
- Successful subscription tier assignments

## Files Modified

- `src/api/auth/services/user-defaults.ts` (new)
- `src/api/auth/services/firebase-auth.ts`
- `src/api/auth/controllers/auth.ts`
- `src/api/auth/services/google-auth.ts`
- `src/api/airtable/services/airtable.ts`
