/**
 * Verification script to check if the subscription tier assignment implementation is correct
 * 
 * This script verifies:
 * 1. The user-defaults service exists and has the correct methods
 * 2. All user creation points have been updated to use the service
 * 3. The implementation follows the expected pattern
 * 
 * Usage: node verify-implementation.js
 */

const fs = require('fs');
const path = require('path');

function checkFileExists(filePath) {
  return fs.existsSync(filePath);
}

function readFileContent(filePath) {
  if (!checkFileExists(filePath)) {
    return null;
  }
  return fs.readFileSync(filePath, 'utf8');
}

function verifyImplementation() {
  console.log('🔍 Verifying subscription tier assignment implementation...\n');

  let allChecksPass = true;

  // Check 1: Verify user-defaults service exists
  console.log('📋 Check 1: Verifying user-defaults service...');
  const userDefaultsPath = 'src/api/auth/services/user-defaults.ts';
  
  if (!checkFileExists(userDefaultsPath)) {
    console.log('❌ user-defaults.ts service not found');
    allChecksPass = false;
  } else {
    const userDefaultsContent = readFileContent(userDefaultsPath);
    
    if (userDefaultsContent.includes('getDefaultSubscriptionTier') && 
        userDefaultsContent.includes('getUserDataWithDefaults') &&
        userDefaultsContent.includes('basic-month')) {
      console.log('✅ user-defaults service exists with correct methods');
    } else {
      console.log('❌ user-defaults service missing required methods');
      allChecksPass = false;
    }
  }

  // Check 2: Verify Firebase auth service
  console.log('\n📋 Check 2: Verifying Firebase auth service...');
  const firebaseAuthPath = 'src/api/auth/services/firebase-auth.ts';
  
  if (!checkFileExists(firebaseAuthPath)) {
    console.log('❌ firebase-auth.ts not found');
    allChecksPass = false;
  } else {
    const firebaseAuthContent = readFileContent(firebaseAuthPath);
    
    if (firebaseAuthContent.includes('user-defaults') && 
        firebaseAuthContent.includes('getUserDataWithDefaults')) {
      console.log('✅ Firebase auth service updated to use user-defaults');
    } else {
      console.log('❌ Firebase auth service not updated');
      allChecksPass = false;
    }
  }

  // Check 3: Verify auth controller
  console.log('\n📋 Check 3: Verifying auth controller...');
  const authControllerPath = 'src/api/auth/controllers/auth.ts';
  
  if (!checkFileExists(authControllerPath)) {
    console.log('❌ auth controller not found');
    allChecksPass = false;
  } else {
    const authControllerContent = readFileContent(authControllerPath);
    
    if (authControllerContent.includes('user-defaults') && 
        authControllerContent.includes('getUserDataWithDefaults')) {
      console.log('✅ Auth controller updated to use user-defaults');
    } else {
      console.log('❌ Auth controller not updated');
      allChecksPass = false;
    }
  }

  // Check 4: Verify Google auth service
  console.log('\n📋 Check 4: Verifying Google auth service...');
  const googleAuthPath = 'src/api/auth/services/google-auth.ts';
  
  if (!checkFileExists(googleAuthPath)) {
    console.log('❌ google-auth.ts not found');
    allChecksPass = false;
  } else {
    const googleAuthContent = readFileContent(googleAuthPath);
    
    if (googleAuthContent.includes('user-defaults') && 
        googleAuthContent.includes('getUserDataWithDefaults')) {
      console.log('✅ Google auth service updated to use user-defaults');
    } else {
      console.log('❌ Google auth service not updated');
      allChecksPass = false;
    }
  }

  // Check 5: Verify Airtable service
  console.log('\n📋 Check 5: Verifying Airtable service...');
  const airtablePath = 'src/api/airtable/services/airtable.ts';
  
  if (!checkFileExists(airtablePath)) {
    console.log('❌ airtable service not found');
    allChecksPass = false;
  } else {
    const airtableContent = readFileContent(airtablePath);
    
    if (airtableContent.includes('user-defaults') && 
        airtableContent.includes('getUserDataWithDefaults')) {
      console.log('✅ Airtable service updated to use user-defaults');
    } else {
      console.log('❌ Airtable service not updated');
      allChecksPass = false;
    }
  }

  // Summary
  console.log('\n' + '='.repeat(60));
  if (allChecksPass) {
    console.log('🎉 All implementation checks passed!');
    console.log('\n📋 Implementation Summary:');
    console.log('   ✓ Created user-defaults service for centralized logic');
    console.log('   ✓ Updated Firebase authentication');
    console.log('   ✓ Updated email/password registration');
    console.log('   ✓ Updated Google authentication');
    console.log('   ✓ Updated Airtable user creation');
    console.log('\n🔧 Next Steps:');
    console.log('   1. Ensure "basic-month" subscription tier exists in admin panel');
    console.log('   2. Test user registration to verify subscription tier assignment');
    console.log('   3. Monitor logs for any errors during user creation');
  } else {
    console.log('❌ Some implementation checks failed!');
    console.log('   Please review the failed checks above and fix the issues.');
  }
  console.log('='.repeat(60));
}

// Run the verification
verifyImplementation();
